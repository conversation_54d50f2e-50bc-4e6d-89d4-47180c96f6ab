/*
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-18 15:00:44
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-18 15:03:22
 * @FilePath: /global-intelligence-web/types/api/agent/messages.d.ts
 * @Description: 
 */
export interface AgentMessagesResType {
  data?: ChatBotMessagesDto[]
  hasMore?: boolean
  limit?: string
}

export interface ChatBotMessagesDto {
  /**
   * 回答消息内容
   */
  answer?: string

  /**
   * 回答处理过程
   */
  answerProcess?: Array<string[]>

  /**
   * 会话id
   */
  conversationId?: string

  /**
   * 消息ID
   */
  id?: string

  /**
   * 用户输入参数
   */
  inputs?: Record<string, any>

  /**
   * 用户输入/提问内容
   */
  query?: string

  [property: string]: any
}
