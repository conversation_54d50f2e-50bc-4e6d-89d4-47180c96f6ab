<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-18 17:37:44
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-21 10:01:53
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/msgItem/userMsgItem.vue
 * @Description: 
-->
<template>
  <div class="my16px flex justify-end fs-16px">
    <div class="inline-block rounded-8px p8px bg-#f5f5f5 color-#000-90">{{ item.content }}</div>
  </div>
</template>

<script setup lang="ts">
export interface UserMsgItemType {
  id: string
  role: 'user'
  content: string
  timestamp: string
}

const props = defineProps<{ item: UserMsgItemType }>()
</script>

<style scoped></style>
