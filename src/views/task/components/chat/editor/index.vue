<template>
  <div class="max-h222px scroller">
    <!-- class="" -->

    <!-- 
      @keydown.left="handleLeftKey"
      @keydown.right="handleRightKey"
      @keydown.delete="handleDeleteKey" -->
    <!-- @keydown.backspace="handleDeleteKey" -->
    <!-- @cut="handleDeleteKey" -->
    <!-- @keydown="handleKeydown" -->
    <div
      :class="['content editor', !props.value ? 'show-placeholder' : '']"
      :spellcheck="false"
      autocorrect="off"
      autocapitalize="off"
      translate="no"
      ref="contentRef"
      contenteditable="true"
      :data-placeholder="placeholder"
      @paste="handlePaste"
      @keydown.enter="handleEnterKey"
      @keydown.delete="handleDeleteKey"
      @input="handleInput"
    >
      <template v-for="item in content" :key="item.id">
        <template v-if="item.type === 'text'">
          {{ item.value }}
        </template>
        <template v-else-if="item.type === 'InputSlot'">
          <AgentInput
            v-model:value="item.value"
            :placeholder="item.props?.placeholder"
            :slot-data="item"
            :ref="el => setAgentFormItemRef(el, item.id)"
            @delete="el => handleDeleteFormItem(el, item)"
          />
        </template>
        <template v-else-if="item.type === 'SelectSlot'">
          <AgentSelect
            v-model:value="item.value"
            :placeholder="item.props?.placeholder"
            :slot-data="item"
            :ref="el => setAgentFormItemRef(el, item.id)"
            @change="handleInput()"
          />
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import AgentInput from './agentInput.vue'
import { randomUUID } from '@/utils/util'
import AgentSelect from './agentSelect.vue'
import { cloneDeep, isEmpty, isNull, values } from 'lodash-es'
import { message } from 'ant-design-vue'

export interface PromptItemType {
  type: string
  id: string
  value: string
  props?: { placeholder: string; options?: string; mode?: 'multiple' | 'single' }
}

interface Props {
  value: string
  sendBtnDisabled: boolean
  placeholder?: string
  agentPrompt?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '给我布置一个任务（可以让我寻找海外商机、解析海外国家政策、跟踪海外行业新闻...）',
  agentPrompt: ''
})
const emit = defineEmits<{
  'update:value': [value?: string] // v-model双向绑定事件
  'update:sendBtnDisabled': [value: boolean] // v-model双向绑定事件
  enter: [value?: string] // v-model双向绑定事件
}>()

const contentRef = ref<HTMLDivElement>()
const content = ref<PromptItemType[]>([])

// AgentInput组件引用管理
const agentFormItemRefs = ref<Map<string, any>>(new Map())

// 解析agentPrompt内容，生成组件
function parseAgentPrompt(text: string): PromptItemType[] {
  text = text.replace(/\n/g, '')
  const result: PromptItemType[] = []
  let lastIndex = 0

  // 匹配 {"type": ...} 这样的 JSON 结构
  const regex = /(\{"type"[^{}]*(?:\{[^{}]*\}[^{}]*)*\})/g

  let match
  while ((match = regex.exec(text)) !== null) {
    const [fullMatch] = match

    // 提取前面的文本
    const precedingText = text.slice(lastIndex, match.index)
    if (precedingText) {
      result.push({ type: 'text', value: precedingText, id: randomUUID() })
    }

    // 尝试解析 JSON
    try {
      const json = JSON.parse(fullMatch)
      result.push({ ...json, value: json.value ? json.value : undefined, id: randomUUID() })
    } catch (e) {
      console.warn('JSON 解析失败:', fullMatch)
      result.push({ type: 'text', value: fullMatch, id: randomUUID() })
    }

    lastIndex = regex.lastIndex
  }

  // 添加最后剩余的文本
  const remainingText = text.slice(lastIndex)
  if (remainingText) {
    result.push({ type: 'text', value: remainingText, id: randomUUID() })
  }

  return result
}

onMounted(() => {
  content.value = parseAgentPrompt(props.agentPrompt)

  nextTick(() => {
    const text = getContentText()
    emit('update:value', text)
  })
})

// 设置AgentFormItem组件引用
function setAgentFormItemRef(el: any, id: string) {
  if (el) {
    agentFormItemRefs.value.set(id, el)
  } else {
    agentFormItemRefs.value.delete(id)
  }
}

// // 处理AgentInput导航事件，直接切换到下一个输入框
// function handleNavigate(direction: 'left' | 'right', slotData: PromptItemType) {
//   console.log('direction: ', direction)
//   console.log('slotData: ', slotData)
//   // 获取所有InputSlot类型的项目
//   const inputSlots = content.value.filter(item => item.type === 'InputSlot')
//   const currentIndex = inputSlots.findIndex(item => item.id === slotData.id)

//   if (currentIndex === -1) return

//   let targetIndex = -1
//   if (direction === 'left' && currentIndex > 0) {
//     targetIndex = currentIndex - 1
//   } else if (direction === 'right' && currentIndex < inputSlots.length - 1) {
//     targetIndex = currentIndex + 1
//   }

//   if (targetIndex !== -1) {
//     const targetSlot = inputSlots[targetIndex]
//     const targetRef = agentFormItemRefs.value.get(targetSlot.id)

//     if (targetRef && targetRef.inputRef) {
//       // 使用nextTick确保DOM更新完成后再聚焦
//       nextTick(() => {
//         targetRef.inputRef?.focus()
//       })
//     }
//   }
// }

// 计算是否有空值
const isHaveEmptyVal = computed(() => {
  // agentPrompt有值，且有插件输入框
  if (!isEmpty(props.agentPrompt) && content.value.filter(item => item.type !== 'text').length !== 0) {
    return content.value.filter(item => item.type !== 'text').some(item => isEmpty(item.value))
  } else {
    // return isEmpty(getContentText())
    return isEmpty(props.value)
  }
})
watch(
  () => props.value,
  newVal => {
    console.log('newVal: ', newVal)
    console.log('!isEmpty(props.agentPrompt): ', !isEmpty(props.agentPrompt))
    console.log(content.value.filter(item => item.type !== 'text'))
  },
  { immediate: true }
) // 监听isHaveEmptyVal变化，并更新sendBtnDisabled
watch(isHaveEmptyVal, newVal => emit('update:sendBtnDisabled', newVal)) // 监听isHaveEmptyVal变化，并更新sendBtnDisabled

// 处理回车事件
async function handleEnterKey(e: KeyboardEvent) {
  const value = getContentText()

  console.log('回车触发', contentRef.value?.innerText)
  console.log('回车触发', value)
  if (e.key === 'Enter') {
    e.preventDefault()
    if (isHaveEmptyVal.value) {
      message.warning('请先填写任务必要信息')
      return
    }
    const value = getContentText()
    emit('enter', value)
  }
}

// 获取内容文本
function getContentText() {
  // 处理HTML内容，同步到v-model上
  const html = cloneDeep(contentRef.value!.innerHTML)
  const temp = document.createElement('div')
  temp.innerHTML = html
  // 处理agentSelect节点
  const agentSelects = temp.querySelectorAll('.agentSelect')
  agentSelects.forEach(agentSelect => {
    const selector = agentSelect.querySelector('.select-content')
    if (selector) {
      // antSelect会出现重复的内容，替换整个agentSelect节点内容为selector内容
      agentSelect.innerHTML = ''
      agentSelect.appendChild(selector)
    }
  })
  // 获取处理后的纯文本
  const value = (temp.innerText || temp.textContent)?.trim()
  if (isEmpty(value) && contentRef.value) {
    contentRef.value.innerHTML = ''
  }
  return value
}

// 处理输入事件
function handleInput(e?: Event) {
  nextTick(() => {
    // 保存当前光标位置
    // const index = getCaretPosition(contentRef.value!)
    const value = getContentText()
    emit('update:value', value)
    // 恢复光标位置
    // setCaretPosition(contentRef.value!, index!)
  })
}

// 处理粘贴事件
function handlePaste(e: ClipboardEvent) {
  e.preventDefault()

  // 获取粘贴的文本内容
  const pastedText = e.clipboardData?.getData('text/plain') || ''
  console.log('pastedText: ', pastedText)

  if (!pastedText) return

  // 清理粘贴的文本：移除换行符和多余的空格
  const cleanedText = pastedText.replace(/\n/g, '').replace(/\s+/g, ' ').trim()
  console.log('cleanedText: ', cleanedText)

  if (!cleanedText) return

  // // 获取当前光标位置
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)

  // 删除选中的内容（如果有）
  range.deleteContents()

  // 插入清理后的文本
  const textNode = document.createTextNode(cleanedText)
  range.insertNode(textNode)

  // 将光标移动到插入文本的末尾
  range.setStartAfter(textNode)
  range.setEndAfter(textNode)
  selection.removeAllRanges()
  selection.addRange(range)

  // 触发input事件以更新数据
  const inputEvent = new Event('input', { bubbles: true })
  contentRef.value?.dispatchEvent(inputEvent)
}

// 处理删除事件
function handleDeleteKey(e: KeyboardEvent) {
  // 对agentInput的删除做特殊处理
  // 获取当前选区
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return
  // 获取当前选区范围
  const range = selection.getRangeAt(0)
  const container = range.startContainer
  const isStart = range.startOffset === 0 // 是否开头
  const isEnd = range.startOffset === container.textContent?.length // 是否结尾
  const preNode = container.previousElementSibling || null
  const nextNode = container.nextElementSibling || null
  console.log(preNode, nextNode)

  const preNodeIsAgentFormItem = !isNull(preNode)
    ? preNode.classList.contains('agentInput') || preNode?.classList.contains('agentSelect')
    : false // 获取上一个元素
  const nextNodeIsAgentFormItem = !isNull(nextNode)
    ? nextNode.classList.contains('agentInput') || nextNode?.classList.contains('agentSelect')
    : false // 获取下一个元素

  // 判断当前元素是否为文本节点，且删除后会导致前后两个agentInput/agentSelect相邻
  const isTextNode = container.nodeType === Node.TEXT_NODE
  const isLastText = isTextNode && container.textContent?.length === 1
  console.log('isLastText: ', isLastText)
  // const preIsFormItem = preNode?.classList.contains('agentInput') || preNode?.classList.contains('agentSelect')
  // const nextIsFormItem = nextNode?.classList.contains('agentInput') || nextNode?.classList.contains('agentSelect')
  if (isLastText && preNodeIsAgentFormItem && nextNodeIsAgentFormItem) {
    // e.preventDefault()
    // console.log(1212)
    // return
    // const textNode = document.createTextNode(' ')
    // range.insertNode(textNode)
  }
  console.log('e.key: ', e.key)
  console.log('isStart: ', isStart)
  console.log('isEnd: ', isEnd)
  console.log('preNodeIsAgentFormItem: ', preNodeIsAgentFormItem)
  console.log('nextNodeIsAgentFormItem: ', nextNodeIsAgentFormItem)
  if (
    (isStart && e.key === 'Backspace' && preNodeIsAgentFormItem) ||
    (isEnd && e.key === 'Delete' && nextNodeIsAgentFormItem)
  ) {
    console.log('e: ', e)
    // e.preventDefault()
    // const id = isEnd ? nextNode?.getAttribute('data-key')! : preNode?.getAttribute('data-key')!
    // handleDeleteFormItem(id)
    // content.value = content.value.filter(item => item.id !== id) // 过滤掉要删除的项目
    // return
  }
}

function handleDeleteFormItem(e: KeyboardEvent, delItem: PromptItemType) {
  // 将光标移动到formItem外面，再进行删除控件
  const domClassName =
    delItem.type === 'InputSlot' ? '.agentInput' : delItem.type === 'SelectSlot' ? '.agentSelect' : null
  if (isNull(domClassName)) {
    return
  }
  e.preventDefault()
  const dom = document.querySelector(`${domClassName}[data-key="${delItem.id}"]`)
  // 将光标移动到dom之前
  const selection = window.getSelection()
  if (selection && dom) {
    const range = document.createRange()
    range.setStartBefore(dom)
    range.setEndBefore(dom)
    selection.removeAllRanges()
    selection.addRange(range)
    nextTick(() => {
      content.value = content.value.filter(item => item.id !== delItem.id) // 过滤掉要删除的项目
    })
  }
}
</script>

<style lang="less" scoped>
.scroller {
  display: flex !important;
  align-items: flex-start !important;
  font-family: monospace;
  line-height: 1.4;
  height: 100%;
  overflow-x: auto;
  position: relative;
  z-index: 0;
  overflow-anchor: none;
}

.content {
  font-size: 16px;
  min-height: 62px;
  cursor: text;
  outline: none;
  width: 100%;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
  tab-size: 2;
  line-height: 36px;

  .textWarper {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: row;
  }

  &[contenteditable='true'] {
    -webkit-user-modify: read-write-plaintext-only;
  }

  // 只在空内容且有 show-placeholder 类时显示 placeholder
  &.show-placeholder:empty::before {
    content: attr(data-placeholder);
    color: #999;
    pointer-events: none;
  }
  // &[data-placeholder]:not(.has-content) {
  //   &:empty,
  //   &:has(br:only-child) {
  //     &:before {
  //       content: attr(data-placeholder);
  //       color: #999;
  //       pointer-events: none;
  //     }
  //   }
  // }
}

@slotHeight: 36px;
// 辅助元素样式
.widgetBuffer {
  vertical-align: text-top;
  height: 1em;
  width: 0;
  display: inline;
}

@slotBgColor: #cfc5f2;
@slotColor: #f4f0ff;

.slot-side-left {
  background-color: @slotBgColor;
  border-radius: 6px 0 0 6px;
  margin-left: 3px;
  height: 24px;
  padding: 2px 0 2px 6px;
}

.slot-side-right {
  background-color: @slotBgColor;
  border-radius: 0 6px 6px 0;
  margin-right: 3px;
  height: 24px;
  padding: 2px 6px 2px 0;
}

.placeholder,
.slot-content {
  color: @slotColor;
  background-color: @slotBgColor;
  word-break: break-all;
  padding: 2px 0;
  line-height: 20px;
}
.slot-content {
  color: @slotColor;
}
</style>
